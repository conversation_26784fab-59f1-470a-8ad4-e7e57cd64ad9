{"devDependencies": {"@biomejs/biome": "2.1.1", "@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@commitlint/prompt-cli": "^19.8.1", "@types/bun": "^1.2.18", "husky": "^9.1.7", "typescript": "^5.8.3"}, "name": "ozaco", "patchedDependencies": {"@clerc/core@0.44.0": "patches/@<EMAIL>"}, "private": "true", "scripts": {"commit": "commit", "prepare": "husky"}, "trustedDependencies": ["@biomejs/biome", "bufferutil", "es5-ext", "utf-8-validate"], "type": "module", "version": "0.0.0", "workspaces": ["{packages,plugins,apps,tools,experiments}/**"]}