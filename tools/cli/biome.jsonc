{
  "$schema": "https://biomejs.dev/schemas/2.1.1/schema.json",

  "extends": "//",

  "linter": {
    "rules": {
      "nursery": {
        "noImportCycles": "off",
      },
      "style": {
        "noInferrableTypes": "error",
        "noParameterAssign": "error",
        "noUnusedTemplateLiteral": "error",
        "noUselessElse": "error",
        "useAsConstAssertion": "error",
        "useDefaultParameterLast": "error",
        "useEnumInitializers": "error",
        "useNumberNamespace": "error",
        "useSelfClosingElements": "error",
        "useSingleVarDeclarator": "error",
      },
      "suspicious": {
        "noConsole": "off",
        "noExplicitAny": "off",
      },
    },
  },
  "root": false,
}
