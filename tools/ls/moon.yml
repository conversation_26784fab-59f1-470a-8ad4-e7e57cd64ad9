language: "typescript"
type: "library"
platform: "bun"

tasks:
  watch:
    local: true
    command: "bunx ozaco build -w -s -f cjs -t node"
    deps:
      - "cli:dev"
      - "std:dev"
    inputs:
      - "src/**/*"
    outputs:
      - "dist/**"
  dev:
    local: true
    command: "bunx ozaco build -f cjs -t node"
    deps:
      - "std:dev"
    inputs:
      - "src/**/*"
    outputs:
      - "dist/**"
  build:
    command: "bunx ozaco build -s -f cjs -t node -m prod"
    deps:
      - "std:build"
    inputs:
      - "src/**/*"
    outputs:
      - "dist/**"
