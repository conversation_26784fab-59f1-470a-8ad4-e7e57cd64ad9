language: "typescript"
type: "library"
platform: "bun"

tasks:
  watch:
    local: true
    command: "bunx ozaco build -w"
    deps:
      - "cli:dev"
      - "std:dev"
    inputs:
      - "src/**/*"
    outputs:
      - "dist/**"
  dev:
    local: true
    command: "bunx ozaco build"
    deps:
      - "cli:dev"
      - "std:dev"
    inputs:
      - "src/**/*"
    outputs:
      - "dist/**"
  build:
    command: "bunx ozaco build -s -m prod"
    deps:
      - "std:build"
    inputs:
      - "src/**/*"
    outputs:
      - "dist/**"
