import { $ulid } from '@ozaco/std/crypto'
import { $fn, Err } from '@ozaco/std/results'
import type { Subscription } from 'nats'

import { natsPluginBase } from '../base'

import { contextAction } from './context'

export const requestAction = natsPluginBase.direct('request', rawCtx => {
  const ctx = rawCtx.tag('no-response').tag('subscription')

  const subscriptions = new Map<string, Subscription>()

  return ctx.apply({
    request: ctx.$safe('request', async function* (request: Std.Messaging.Request.Request) {
      const { client, codec } = yield* ctx.$peek(contextAction)
      const body = codec.encode(request)

      const result = await client.request(request.subject, body, {
        timeout: request.options.timeout ?? 1000,
      })

      const response = codec.decode(result.data)

      if (typeof response === 'object' && response && 'name' in response && 'causes' in response) {
        const error = response as Err

        yield* new Err(error.name, error.message, error.causes, error.data, error.timestamp)
      }

      return {
        body: response,

        request,
      } as Std.Messaging.Request.Response
    }),

    subscribe: ctx.$safe(
      'subscribe',
      // biome-ignore lint/suspicious/useAwait: Redundant
      async function* (subject: string, cb: (request: Std.Messaging.Request.Request) => unknown) {
        const { client, codec } = yield* ctx.$peek(contextAction)
        const identifier = yield* $ulid()
        const subscription = client.subscribe(subject)
        const wrapped = $fn(cb, ctx.tags.get('request/subscription'))

        // biome-ignore lint/nursery/noFloatingPromises: Redundant
        ;(async () => {
          for await (const message of subscription) {
            const request = codec.decode(message.data) as Std.Messaging.Request.Request

            const result = wrapped(request)

            if (result.isErr()) {
              message.respond(codec.encode(result))

              continue
            }

            message.respond(codec.encode(result.value))
          }
        })()

        subscriptions.set(identifier, subscription)

        return identifier
      },
    ),

    unsubscribe: ctx.$fn('unsubscribe', (identifier: string) => {
      const subscription = subscriptions.get(identifier)

      if (!subscription) {
        return
      }

      subscription.unsubscribe()
    }),
  })
})
