import { $ulid } from '@ozaco/std/crypto'
import { err } from '@ozaco/std/results'

import { messagingPluginBase } from '../base'

export const requestAction = messagingPluginBase.direct('request', rawCtx => {
  const ctx = rawCtx.tag('no-response')

  let simpleId = 0

  const generateId = ctx.$safe('generate-id', function* () {
    if (ctx.meta.options[0].mode === 'prod') {
      return (yield* $ulid()) as string
    }

    return `simple-${++simpleId}`
  })

  return ctx.apply({
    generateId,

    request: ctx.$safe(
      'request',
      async function* (subject: string, body: string | ArrayBuffer, options: Std.Messaging.Request.Init = {}) {
        options.id = options.id ?? (yield* generateId())

        const dependencyEntries = Object.entries(ctx.dependencies)
        const sortedDependencyEntries = dependencyEntries.sort((a, b) => {
          return a[1].meta.options[0].order - b[1].meta.options[0].order
        })

        let response: Std.Messaging.Request.Response | undefined
        const calledTransports: string[] = []

        for (const [name, transport] of sortedDependencyEntries) {
          calledTransports.push(name)

          const $response = await transport.request({
            body,
            from: ctx.meta.options[0].name,
            options,
            subject,
            to: options.to ?? '*',
          })

          if ($response.isErr()) {
            continue
          }

          response = $response.value
        }

        if (!response) {
          return err(ctx.tags.get('not-found'), 'no response').appendData({
            calledTransports,
            options,
            subject,
          })
        }

        return response
      },
    ),

    subscribe: ctx.$safe(
      'subscribe',
      // biome-ignore lint/suspicious/useAwait: Redundant
      async function* (subject: string, cb: (request: Std.Messaging.Request.Request) => void) {
        const dependencyEntries = Object.entries(ctx.dependencies)
        const sortedDependencyEntries = dependencyEntries.sort((a, b) => {
          return a[1].meta.options[0].order - b[1].meta.options[0].order
        })

        const identifiers: Record<string, string> = {}

        for (const [name, transport] of sortedDependencyEntries) {
          const subscriptionIdentifier = yield* transport.subscribe(subject, cb)

          identifiers[name] = subscriptionIdentifier
        }

        return identifiers
      },
    ),

    unsubscribe: ctx.$safe(
      'unsubscribe',
      // biome-ignore lint/suspicious/useAwait: Redundant
      async function* (identifier: string) {
        const dependencyEntries = Object.entries(ctx.dependencies)
        const sortedDependencyEntries = dependencyEntries.sort((a, b) => {
          return a[1].meta.options[0].order - b[1].meta.options[0].order
        })

        for (const [name, transport] of sortedDependencyEntries) {
          yield* transport.unsubscribe(identifier)
        }
      },
    ),
  })
})
