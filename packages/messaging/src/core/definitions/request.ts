import type { BlobType, Fn } from '@ozaco/std/shared'

declare global {
  namespace Std {
    namespace Messaging {
      namespace Request {
        interface Request {
          from: string
          to?: Std.Messaging.Request.Init['to']

          subject: string
          body: string | ArrayBuffer

          options: Omit<Std.Messaging.Request.Init, 'to'>
        }

        interface Response {
          request: Std.Messaging.Request.Request

          body: string | ArrayBuffer
        }

        interface Init {
          timeout?: number
          retry?: number
          retryDelay?: number

          id?: string
          to?: string | string[]
        }

        interface Api {
          request: Fn<
            [request: Std.Messaging.Request.Request],
            Std.Both<Std.Messaging.Request.Response, BlobType, BlobType[]>
          >
          subscribe: Fn<
            [subject: string, cb: (request: Std.Messaging.Request.Request) => void],
            Std.Both<string, BlobType, BlobType[]>
          >
          unsubscribe: Fn<[subject: string], Std.Both<void, BlobType, BlobType[]>>
        }
      }
    }
  }
}
