import type { BlobType } from '@ozaco/std/shared'

declare global {
  namespace Std {
    namespace Messaging {
      type Mode = 'dev' | 'prod'

      type Options = [
        {
          name: string
          mode?: Mode
        },
      ]

      type AnyTransport = Std.Plugin.Sync<
        {
          name: BlobType
          version: BlobType
          options: [
            {
              order: number
            },
          ]
        },
        Std.Messaging.Request.Api,
        BlobType,
        BlobType
      >
    }
  }
}
