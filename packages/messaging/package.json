{"type": "module", "version": "0.0.0", "name": "@ozaco/messaging", "dependencies": {"@ozaco/std": "workspace:*"}, "devDependencies": {"@ozaco/cli": "workspace:*", "nats": "^2.29.3"}, "peerDependencies": {"nats": "=> 2.29.3"}, "exports": {".": {"default": "./dist/core.js", "source": "./src/core/index.ts", "types": "./dist/core.d.ts"}, "./nats": {"default": "./dist/transports-nats.js", "source": "./src/nats/index.ts", "types": "./dist/transports-nats.d.ts"}}, "files": ["dist"], "author": "giveerr (https://github.com/giveerr)", "homepage": "https://ozaco.com/", "repository": {"type": "git", "url": "https://github.com/ozaco/ozaco.git"}}