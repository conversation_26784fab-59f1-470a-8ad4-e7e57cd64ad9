import { createMessaging } from '../src/core'
import { createNats } from '../src/nats'

export const nats = await createNats({
  order: 1,
  urls: ['nats://localhost:4222'],
})

// @ts-expect-error
// biome-ignore lint/correctness/noUndeclaredVariables: Redundant
export const eventLoop = await createEventLoop({
  order: 0,
  urls: ['nats://localhost:4222'],
})

export const messaging = createMessaging({
  mode: 'prod',
  name: 'test',
})
  .use('nats', nats)
  .use('event-loop', eventLoop)
