import type { BlobType, Fn } from '../../shared'

import { resultTags } from '../tag'
import { handleCatch, handleThen } from './internal/handlers'

const invalidUsage = resultTags.get('invalid-usage')

/**
 * The $fn function is used to wrap a function that returns a Result or ResultAsync
 * and automatically handles the error cases.
 */
export const $fn = <A extends BlobType[], R, C extends Std.ErrorValues[] = []>(
  fn: (...args: A) => R,
  ...additionalCauses: C
) => {
  return ((...args: A) => {
    try {
      const data = fn(...args)

      return handleThen(data, ...additionalCauses)
    } catch (rawError) {
      return handleCatch(rawError, ...additionalCauses)
    }
  }) as Fn<A, Std.InjectedResult<R, typeof invalidUsage, C>>
}
